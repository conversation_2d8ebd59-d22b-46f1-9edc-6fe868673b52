# 项目结构说明

## 📁 重构后的包结构

经过重构，项目现在采用了更加清晰和模块化的包结构：

```
src/main/java/com/sheep/threadpool/
├── core/                           # 核心线程池实现
│   ├── MyThreadPool.java          # 主线程池类
│   └── PoolState.java             # 线程池状态枚举
├── factory/                        # 线程工厂相关
│   ├── DefaultThreadFactory.java  # 默认线程工厂
│   ├── NamedThreadFactory.java    # 命名线程工厂
│   └── MonitoringThreadFactory.java # 监控线程工厂
├── reject/                         # 拒绝策略相关
│   ├── RejectHandle.java          # 拒绝策略接口
│   ├── DiscardRejectHandle.java   # 丢弃最旧任务策略
│   ├── DiscardPolicyRejectHandle.java # 简单丢弃策略
│   ├── CallerRunsPolicyRejectHandle.java # 调用者运行策略
│   └── ThrowRejectHandle.java     # 抛出异常策略
├── worker/                         # 工作线程相关
│   ├── CoreWorker.java            # 核心线程工作器
│   └── SupportWorker.java         # 支持线程工作器
├── demo/                           # 演示和测试
│   ├── Main.java                  # 主要功能演示
│   └── ThreadFactoryDemo.java     # 线程工厂演示
└── util/                           # 工具类
    └── ThreadPoolUtils.java       # 线程池工具类

src/test/java/com/sheep/threadpool/
└── MyThreadPoolTest.java          # 单元测试
```

## 🎯 包设计原则

### 1. **单一职责原则**
每个包都有明确的职责：
- `core` - 核心线程池逻辑
- `factory` - 线程创建相关
- `reject` - 拒绝策略相关
- `worker` - 工作线程相关
- `demo` - 演示和测试
- `util` - 工具和辅助功能

### 2. **依赖关系清晰**
```
core (核心) ← worker (工作线程)
core (核心) ← reject (拒绝策略)
core (核心) ← factory (线程工厂)
core (核心) ← demo (演示)
core (核心) ← util (工具)
```

### 3. **高内聚低耦合**
- 每个包内的类紧密相关
- 包之间的依赖关系简单明确
- 核心包提供稳定的API接口

## 📦 各包详细说明

### core 包 - 核心实现
**职责**：线程池的核心逻辑和状态管理

**主要类**：
- `MyThreadPool` - 主线程池类，提供所有核心功能
- `PoolState` - 线程池状态枚举（RUNNING、SHUTDOWN、TERMINATED）

**设计特点**：
- 提供完整的线程池API
- 管理线程生命周期
- 处理任务调度逻辑

### factory 包 - 线程工厂
**职责**：负责线程的创建和定制

**主要类**：
- `DefaultThreadFactory` - 基础线程工厂
- `NamedThreadFactory` - 支持自定义命名的线程工厂
- `MonitoringThreadFactory` - 带监控功能的线程工厂

**设计特点**：
- 实现策略模式
- 支持线程属性定制
- 提供监控和统计功能

### reject 包 - 拒绝策略
**职责**：处理线程池无法接受新任务时的逻辑

**主要类**：
- `RejectHandle` - 拒绝策略接口
- `DiscardRejectHandle` - 丢弃最旧任务
- `CallerRunsPolicyRejectHandle` - 调用者运行
- `ThrowRejectHandle` - 抛出异常
- `DiscardPolicyRejectHandle` - 静默丢弃

**设计特点**：
- 实现策略模式
- 提供多种拒绝处理方式
- 易于扩展新的策略

### worker 包 - 工作线程
**职责**：实现具体的线程工作逻辑

**主要类**：
- `CoreWorker` - 核心线程工作器（永久运行）
- `SupportWorker` - 支持线程工作器（有超时机制）

**设计特点**：
- 分离核心线程和支持线程逻辑
- 处理线程中断和异常
- 管理线程生命周期

### demo 包 - 演示代码
**职责**：展示线程池的各种功能和用法

**主要类**：
- `Main` - 主要功能演示
- `ThreadFactoryDemo` - 线程工厂功能演示

**设计特点**：
- 提供完整的使用示例
- 演示各种配置和策略
- 便于学习和测试

### util 包 - 工具类
**职责**：提供便利的工具方法和预配置的线程池

**主要类**：
- `ThreadPoolUtils` - 线程池工具类

**功能**：
- 快速创建常用类型的线程池
- 提供状态监控工具
- 系统配置推荐

## 🔧 使用指南

### 基本使用
```java
import com.sheep.threadpool.core.MyThreadPool;
import com.sheep.threadpool.factory.NamedThreadFactory;
import com.sheep.threadpool.reject.CallerRunsPolicyRejectHandle;

// 创建线程池
MyThreadPool threadPool = new MyThreadPool(
    2, 4, 60, TimeUnit.SECONDS,
    new ArrayBlockingQueue<>(100),
    new CallerRunsPolicyRejectHandle(),
    new NamedThreadFactory("MyApp")
);
```

### 使用工具类
```java
import com.sheep.threadpool.util.ThreadPoolUtils;

// 快速创建IO密集型线程池
MyThreadPool ioPool = ThreadPoolUtils.newIOIntensiveThreadPool("IOWorker");

// 快速创建CPU密集型线程池
MyThreadPool cpuPool = ThreadPoolUtils.newCPUIntensiveThreadPool("CPUWorker");
```

### 自定义拒绝策略
```java
import com.sheep.threadpool.reject.RejectHandle;

RejectHandle customReject = (task, pool) -> {
    // 自定义拒绝逻辑
    System.out.println("Custom reject logic");
};
```

## 🎓 设计优势

### 1. **可维护性**
- 代码按功能模块组织
- 职责分离，易于定位问题
- 接口设计清晰

### 2. **可扩展性**
- 策略模式支持新的拒绝策略
- 工厂模式支持新的线程工厂
- 工作器模式支持新的线程类型

### 3. **可测试性**
- 每个包可以独立测试
- 依赖关系简单，易于模拟
- 提供完整的测试用例

### 4. **可读性**
- 包名直观反映功能
- 类名遵循命名规范
- 详细的注释和文档

## 📈 后续扩展建议

1. **添加监控包** - 专门处理线程池监控和指标收集
2. **添加配置包** - 支持外部配置文件
3. **添加异常包** - 定义专门的异常类型
4. **添加调度包** - 支持定时任务功能

这种包结构为项目的长期发展奠定了良好的基础，既保持了代码的清晰性，又为未来的功能扩展留下了空间。
