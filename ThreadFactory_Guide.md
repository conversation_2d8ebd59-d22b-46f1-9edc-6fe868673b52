# ThreadFactory 详解指南

## 🏭 什么是 ThreadFactory？

**ThreadFactory** 是Java并发包中的一个接口，用于创建新线程。它是线程池的重要组成部分，负责定制线程的创建过程。

```java
public interface ThreadFactory {
    Thread newThread(Runnable r);
}
```

## 🎯 ThreadFactory 的作用

### 1. **自定义线程名称**
- 给线程起有意义的名字，便于调试和日志分析
- 区分不同业务模块的线程

### 2. **设置线程属性**
- **优先级**：设置线程的执行优先级
- **守护线程**：决定是否为守护线程
- **线程组**：将线程归类到特定的线程组

### 3. **统一线程创建逻辑**
- 集中管理线程的创建过程
- 确保所有线程都遵循相同的创建规则

### 4. **监控和统计**
- 记录创建的线程数量
- 监控线程的生命周期
- 收集性能统计信息

### 5. **异常处理**
- 设置未捕获异常处理器
- 统一处理线程异常

## 🔧 ThreadFactory 的实现

### 1. 默认线程工厂
```java
private static class DefaultThreadFactory implements ThreadFactory {
    private final AtomicInteger threadNumber = new AtomicInteger(1);
    private final String namePrefix = "MyThreadPool-Thread-";

    @Override
    public Thread newThread(Runnable r) {
        Thread thread = new Thread(r, namePrefix + threadNumber.getAndIncrement());
        thread.setDaemon(false);
        thread.setPriority(Thread.NORM_PRIORITY);
        return thread;
    }
}
```

### 2. 命名线程工厂
```java
public class NamedThreadFactory implements ThreadFactory {
    private final AtomicInteger threadNumber = new AtomicInteger(1);
    private final String namePrefix;
    
    public NamedThreadFactory(String namePrefix) {
        this.namePrefix = namePrefix;
    }
    
    @Override
    public Thread newThread(Runnable r) {
        Thread thread = new Thread(r, namePrefix + "-" + threadNumber.getAndIncrement());
        thread.setDaemon(false);
        thread.setPriority(Thread.NORM_PRIORITY);
        
        // 设置未捕获异常处理器
        thread.setUncaughtExceptionHandler((t, e) -> {
            System.err.println("Thread " + t.getName() + " threw exception: " + e.getMessage());
        });
        
        return thread;
    }
}
```

### 3. 监控线程工厂
```java
public class MonitoringThreadFactory implements ThreadFactory {
    private final AtomicLong totalCreatedThreads = new AtomicLong(0);
    private final AtomicInteger activeThreads = new AtomicInteger(0);
    
    @Override
    public Thread newThread(Runnable r) {
        Runnable wrappedTask = () -> {
            activeThreads.incrementAndGet();
            try {
                r.run();
            } finally {
                activeThreads.decrementAndGet();
            }
        };
        
        Thread thread = new Thread(wrappedTask, "Monitored-" + totalCreatedThreads.incrementAndGet());
        return thread;
    }
}
```

## 🚀 使用场景

### 1. **业务线程池区分**
```java
// 订单处理线程池
ThreadFactory orderFactory = new NamedThreadFactory("OrderProcessor");
MyThreadPool orderPool = new MyThreadPool(5, 10, 60, TimeUnit.SECONDS, 
    new ArrayBlockingQueue<>(100), new CallerRunsPolicyRejectHandle(), orderFactory);

// 支付处理线程池
ThreadFactory paymentFactory = new NamedThreadFactory("PaymentProcessor");
MyThreadPool paymentPool = new MyThreadPool(3, 6, 60, TimeUnit.SECONDS, 
    new ArrayBlockingQueue<>(50), new ThrowRejectHandle(), paymentFactory);
```

### 2. **高优先级任务处理**
```java
ThreadFactory highPriorityFactory = r -> {
    Thread thread = new Thread(r, "HighPriority-" + System.currentTimeMillis());
    thread.setPriority(Thread.MAX_PRIORITY);
    return thread;
};
```

### 3. **守护线程池**
```java
ThreadFactory daemonFactory = r -> {
    Thread thread = new Thread(r, "Daemon-Worker");
    thread.setDaemon(true); // 设置为守护线程
    return thread;
};
```

### 4. **带监控的线程池**
```java
MonitoringThreadFactory monitoringFactory = new MonitoringThreadFactory("WebServer");
MyThreadPool webServerPool = new MyThreadPool(10, 20, 300, TimeUnit.SECONDS,
    new ArrayBlockingQueue<>(1000), new DiscardRejectHandle(), monitoringFactory);

// 定期打印统计信息
ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
scheduler.scheduleAtFixedRate(() -> {
    monitoringFactory.printStatistics();
}, 0, 30, TimeUnit.SECONDS);
```

## 📊 ThreadFactory vs 默认创建

| 特性 | 默认Thread创建 | 使用ThreadFactory |
|------|----------------|-------------------|
| 线程命名 | Thread-0, Thread-1... | 自定义有意义的名称 |
| 属性设置 | 默认属性 | 可自定义优先级、守护状态等 |
| 异常处理 | 默认处理 | 可自定义异常处理逻辑 |
| 监控统计 | 无 | 可添加监控和统计功能 |
| 调试友好性 | 较差 | 非常友好 |

## 🎓 最佳实践

### 1. **总是使用有意义的线程名称**
```java
// ❌ 不好的做法
new Thread(task).start();

// ✅ 好的做法
ThreadFactory factory = new NamedThreadFactory("DataProcessor");
Thread thread = factory.newThread(task);
thread.start();
```

### 2. **为不同业务使用不同的ThreadFactory**
```java
// 不同业务模块使用不同的线程工厂
ThreadFactory userServiceFactory = new NamedThreadFactory("UserService");
ThreadFactory orderServiceFactory = new NamedThreadFactory("OrderService");
ThreadFactory notificationFactory = new NamedThreadFactory("Notification");
```

### 3. **添加异常处理**
```java
thread.setUncaughtExceptionHandler((t, e) -> {
    logger.error("Thread {} encountered uncaught exception", t.getName(), e);
    // 可以添加告警、重启逻辑等
});
```

### 4. **考虑线程的生命周期**
```java
// 对于后台服务，可能需要守护线程
if (isBackgroundService) {
    thread.setDaemon(true);
}
```

## 🔍 调试和监控

使用ThreadFactory后，调试变得更加容易：

```bash
# 线程堆栈信息更清晰
"OrderProcessor-1" #10 prio=5 os_prio=0 tid=0x... nid=0x... waiting on condition
"PaymentProcessor-2" #11 prio=5 os_prio=0 tid=0x... nid=0x... runnable
"NotificationSender-3" #12 prio=5 os_prio=0 tid=0x... nid=0x... waiting for monitor entry
```

## 📝 总结

ThreadFactory是线程池的重要组成部分，它提供了：

- ✅ **更好的可读性** - 有意义的线程名称
- ✅ **更强的控制力** - 自定义线程属性
- ✅ **更好的监控** - 统计和监控功能
- ✅ **更好的调试** - 清晰的线程标识
- ✅ **更好的维护** - 统一的线程创建逻辑

在生产环境中，强烈建议为每个线程池配置合适的ThreadFactory，这将大大提升系统的可观测性和可维护性。
