package com.sheep.threadpool;

import com.sheep.threadpool.core.MyThreadPool;
import com.sheep.threadpool.factory.NamedThreadFactory;
import com.sheep.threadpool.reject.DiscardPolicyRejectHandle;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 线程池单元测试
 * 验证线程池的各项功能是否正常工作
 */
public class MyThreadPoolTest {
    
    public static void main(String[] args) throws InterruptedException {
        System.out.println("=== 线程池单元测试开始 ===");
        
        testBasicExecution();
        testCoreThreadCreation();
        testSupportThreadCreation();
        testQueueCapacity();
        testRejectPolicy();
        testShutdown();
        
        System.out.println("=== 所有测试通过 ===");
    }
    
    /**
     * 测试基本任务执行功能
     */
    public static void testBasicExecution() throws InterruptedException {
        System.out.println("\n--- 测试基本任务执行 ---");
        
        MyThreadPool threadPool = new MyThreadPool(
            2, 4, 1, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(2),
            new DiscardPolicyRejectHandle()
        );
        
        AtomicInteger counter = new AtomicInteger(0);
        CountDownLatch latch = new CountDownLatch(3);
        
        // 提交3个任务
        for (int i = 0; i < 3; i++) {
            threadPool.execute(() -> {
                counter.incrementAndGet();
                latch.countDown();
            });
        }
        
        // 等待任务完成
        latch.await(2, TimeUnit.SECONDS);
        
        // 验证结果
        assert counter.get() == 3 : "Expected 3 tasks executed, but got " + counter.get();
        System.out.println("✓ 基本任务执行测试通过");
        
        threadPool.shutdown();
    }
    
    /**
     * 测试核心线程创建
     */
    public static void testCoreThreadCreation() throws InterruptedException {
        System.out.println("\n--- 测试核心线程创建 ---");
        
        MyThreadPool threadPool = new MyThreadPool(
            3, 5, 1, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(1),
            new DiscardPolicyRejectHandle()
        );
        
        CountDownLatch latch = new CountDownLatch(3);
        
        // 提交3个长时间运行的任务，应该创建3个核心线程
        for (int i = 0; i < 3; i++) {
            threadPool.execute(() -> {
                try {
                    Thread.sleep(500);
                    latch.countDown();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
        }
        
        // 短暂等待让线程创建
        Thread.sleep(100);
        
        // 验证活跃线程数
        int activeCount = threadPool.getActiveCount();
        assert activeCount == 3 : "Expected 3 active threads, but got " + activeCount;
        System.out.println("✓ 核心线程创建测试通过，活跃线程数: " + activeCount);
        
        latch.await(2, TimeUnit.SECONDS);
        threadPool.shutdown();
    }
    
    /**
     * 测试支持线程创建
     */
    public static void testSupportThreadCreation() throws InterruptedException {
        System.out.println("\n--- 测试支持线程创建 ---");
        
        MyThreadPool threadPool = new MyThreadPool(
            1, 3, 1, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(1),
            new DiscardPolicyRejectHandle()
        );
        
        CountDownLatch latch = new CountDownLatch(3);
        
        // 提交3个任务：1个核心线程 + 1个队列 + 1个支持线程
        for (int i = 0; i < 3; i++) {
            threadPool.execute(() -> {
                try {
                    Thread.sleep(500);
                    latch.countDown();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
        }
        
        // 短暂等待让线程创建
        Thread.sleep(100);
        
        // 验证活跃线程数（应该有2个：1个核心 + 1个支持）
        int activeCount = threadPool.getActiveCount();
        assert activeCount == 2 : "Expected 2 active threads, but got " + activeCount;
        System.out.println("✓ 支持线程创建测试通过，活跃线程数: " + activeCount);
        
        latch.await(2, TimeUnit.SECONDS);
        threadPool.shutdown();
    }
    
    /**
     * 测试队列容量
     */
    public static void testQueueCapacity() throws InterruptedException {
        System.out.println("\n--- 测试队列容量 ---");
        
        MyThreadPool threadPool = new MyThreadPool(
            1, 1, 1, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(2),
            new DiscardPolicyRejectHandle()
        );
        
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch endLatch = new CountDownLatch(1);
        
        // 提交一个长时间运行的任务占用唯一的线程
        threadPool.execute(() -> {
            startLatch.countDown();
            try {
                endLatch.await();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
        
        // 等待第一个任务开始
        startLatch.await();
        
        // 提交2个任务到队列
        threadPool.execute(() -> {});
        threadPool.execute(() -> {});
        
        // 验证队列大小
        int queueSize = threadPool.getQueueSize();
        assert queueSize == 2 : "Expected queue size 2, but got " + queueSize;
        System.out.println("✓ 队列容量测试通过，队列大小: " + queueSize);
        
        endLatch.countDown();
        threadPool.shutdown();
    }
    
    /**
     * 测试拒绝策略
     */
    public static void testRejectPolicy() throws InterruptedException {
        System.out.println("\n--- 测试拒绝策略 ---");
        
        AtomicInteger rejectedCount = new AtomicInteger(0);
        
        MyThreadPool threadPool = new MyThreadPool(
            1, 1, 1, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(1),
            (task, pool) -> rejectedCount.incrementAndGet()
        );
        
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch endLatch = new CountDownLatch(1);
        
        // 占用唯一的线程
        threadPool.execute(() -> {
            startLatch.countDown();
            try {
                endLatch.await();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
        
        startLatch.await();
        
        // 填满队列
        threadPool.execute(() -> {});
        
        // 这个任务应该被拒绝
        threadPool.execute(() -> {});
        
        // 验证拒绝计数
        assert rejectedCount.get() == 1 : "Expected 1 rejected task, but got " + rejectedCount.get();
        System.out.println("✓ 拒绝策略测试通过，拒绝任务数: " + rejectedCount.get());
        
        endLatch.countDown();
        threadPool.shutdown();
    }
    
    /**
     * 测试线程池关闭
     */
    public static void testShutdown() throws InterruptedException {
        System.out.println("\n--- 测试线程池关闭 ---");
        
        MyThreadPool threadPool = new MyThreadPool(
            2, 2, 1, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(1),
            new DiscardPolicyRejectHandle()
        );
        
        // 提交一个任务
        threadPool.execute(() -> {
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
        
        // 关闭线程池
        threadPool.shutdown();
        
        // 验证状态
        assert threadPool.isShutdown() : "ThreadPool should be shutdown";
        System.out.println("✓ 线程池关闭测试通过");
        
        // 尝试提交新任务应该失败
        try {
            threadPool.execute(() -> {});
            assert false : "Should throw exception when submitting to shutdown pool";
        } catch (IllegalStateException e) {
            System.out.println("✓ 关闭后拒绝新任务测试通过");
        }
    }
}
