package com.sheep.jvm;

class Solution {
    public boolean canFinish(int numCourses, int[][] prerequisites) {
        // 初始化入度数组，记录每个课程的先修课程数量
        int[] indegree = new int[numCourses];

        // 计算所有课程的入度（前置课程数量）
        for (int[] prereq : prerequisites) {
            indegree[prereq[0]]++;  // prereq[0]是当前课程，需要先完成prereq[1]
        }

        // 使用数组模拟队列（环形缓冲区实现），避免对象创建开销
        int[] queue = new int[numCourses];
        int front = 0, rear = 0;  // 队列头尾指针

        // 初始化队列：将所有入度为0的课程加入队列
        for (int i = 0; i < numCourses; i++) {
            if (indegree[i] == 0) {
                queue[rear++] = i;  // 入队操作
            }
        }

        int count = rear;  // 记录已处理的无前置课程数量

        // 构建邻接表的紧凑表示（优化内存访问）
        int[][] graph = new int[numCourses][];  // 邻接表
        int[] sizes = new int[numCourses];      // 记录每个课程的后续课程数量

        // 第一次遍历：统计每个课程的出度（后续课程数量）
        for (int[] prereq : prerequisites) {
            sizes[prereq[1]]++;  // prereq[1]是前置课程，指向prereq[0]
        }

        // 为每个课程分配精确大小的数组（避免动态扩容）
        for (int i = 0; i < numCourses; i++) {
            graph[i] = new int[sizes[i]];
        }

        // 第二次遍历：填充邻接表
        int[] pointers = new int[numCourses];  // 记录每个课程在邻接表中的填充位置
        for (int[] prereq : prerequisites) {
            int preCourse = prereq[1];
            int course = prereq[0];
            graph[preCourse][pointers[preCourse]++] = course;  // 添加后续课程
        }

        // 拓扑排序核心逻辑
        while (front < rear) {
            int current = queue[front++];  // 出队一个无前置课程

            // 遍历当前课程的所有后续课程
            for (int neighbor : graph[current]) {
                if (--indegree[neighbor] == 0) {  // 减少后续课程的入度
                    queue[rear++] = neighbor;     // 入队新产生的无前置课程
                    count++;                      // 更新已处理课程数
                }
            }
        }

        // 如果所有课程都被处理过，说明无环，可以完成所有课程
        return count == numCourses;
    }
}