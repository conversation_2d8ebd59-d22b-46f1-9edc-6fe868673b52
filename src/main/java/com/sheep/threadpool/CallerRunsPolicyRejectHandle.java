package com.sheep.threadpool;

/**
 * 调用者运行策略
 * 当线程池无法接受新任务时，由调用线程直接执行任务
 * 这种策略可以减缓任务提交的速度，起到自然的背压作用
 */
public class CallerRunsPolicyRejectHandle implements RejectHandle {
    
    @Override
    public void reject(Runnable rejectCommand, MyThreadPool threadPool) {
        // 检查线程池是否还在运行
        if (!threadPool.isShutdown()) {
            // 由调用线程直接执行任务
            System.out.println("Task executed by caller thread: " + Thread.currentThread().getName());
            rejectCommand.run();
        } else {
            // 线程池已关闭，丢弃任务
            System.out.println("Task discarded because thread pool is shutdown");
        }
    }
}
