package com.sheep.threadpool;

import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 命名线程工厂
 * 可以自定义线程名称前缀，便于调试和监控
 */
public class NamedThreadFactory implements ThreadFactory {
    
    /** 线程计数器，确保每个线程有唯一的编号 */
    private final AtomicInteger threadNumber = new AtomicInteger(1);
    
    /** 线程名称前缀 */
    private final String namePrefix;
    
    /** 是否为守护线程 */
    private final boolean daemon;
    
    /** 线程优先级 */
    private final int priority;
    
    /**
     * 构造函数：使用默认设置
     * 
     * @param namePrefix 线程名称前缀
     */
    public NamedThreadFactory(String namePrefix) {
        this(namePrefix, false, Thread.NORM_PRIORITY);
    }
    
    /**
     * 构造函数：完整设置
     * 
     * @param namePrefix 线程名称前缀
     * @param daemon 是否为守护线程
     * @param priority 线程优先级
     */
    public NamedThreadFactory(String namePrefix, boolean daemon, int priority) {
        this.namePrefix = namePrefix;
        this.daemon = daemon;
        this.priority = priority;
    }
    
    @Override
    public Thread newThread(Runnable r) {
        // 创建线程并设置名称
        Thread thread = new Thread(r, namePrefix + "-" + threadNumber.getAndIncrement());
        
        // 设置守护线程状态
        thread.setDaemon(daemon);
        
        // 设置线程优先级
        thread.setPriority(priority);
        
        // 设置未捕获异常处理器
        thread.setUncaughtExceptionHandler((t, e) -> {
            System.err.println("Thread " + t.getName() + " threw uncaught exception: " + e.getMessage());
            e.printStackTrace();
        });
        
        return thread;
    }
    
    /**
     * 获取已创建的线程数量
     */
    public int getCreatedThreadCount() {
        return threadNumber.get() - 1;
    }
}
