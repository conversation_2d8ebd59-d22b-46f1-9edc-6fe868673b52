package com.sheep.threadpool;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;

/**
 * ThreadFactory 功能演示
 * 展示不同线程工厂的使用效果
 */
public class ThreadFactoryDemo {
    
    public static void main(String[] args) throws InterruptedException {
        System.out.println("=== ThreadFactory 功能演示 ===\n");
        
        // 演示默认线程工厂
        testDefaultThreadFactory();
        Thread.sleep(2000);
        
        // 演示命名线程工厂
        testNamedThreadFactory();
        Thread.sleep(2000);
        
        // 演示监控线程工厂
        testMonitoringThreadFactory();
        Thread.sleep(2000);
        
        // 演示自定义线程工厂
        testCustomThreadFactory();
        
        System.out.println("\n=== 所有演示完成 ===");
    }
    
    /**
     * 测试默认线程工厂
     */
    private static void testDefaultThreadFactory() {
        System.out.println("--- 测试默认线程工厂 ---");
        
        MyThreadPool threadPool = new MyThreadPool(
            2, 3, 1, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(2),
            new DiscardPolicyRejectHandle()
            // 使用默认线程工厂
        );
        
        // 提交任务
        for (int i = 0; i < 4; i++) {
            final int taskId = i;
            threadPool.execute(() -> {
                System.out.println("Default factory task " + taskId + 
                    " executed by " + Thread.currentThread().getName());
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
        }
        
        try {
            Thread.sleep(1500);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        threadPool.shutdown();
        System.out.println();
    }
    
    /**
     * 测试命名线程工厂
     */
    private static void testNamedThreadFactory() {
        System.out.println("--- 测试命名线程工厂 ---");
        
        NamedThreadFactory namedFactory = new NamedThreadFactory("BusinessWorker");
        
        MyThreadPool threadPool = new MyThreadPool(
            2, 3, 1, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(2),
            new DiscardPolicyRejectHandle(),
            namedFactory
        );
        
        // 提交任务
        for (int i = 0; i < 4; i++) {
            final int taskId = i;
            threadPool.execute(() -> {
                System.out.println("Named factory task " + taskId + 
                    " executed by " + Thread.currentThread().getName());
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
        }
        
        try {
            Thread.sleep(1500);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        System.out.println("Created threads count: " + namedFactory.getCreatedThreadCount());
        threadPool.shutdown();
        System.out.println();
    }
    
    /**
     * 测试监控线程工厂
     */
    private static void testMonitoringThreadFactory() {
        System.out.println("--- 测试监控线程工厂 ---");
        
        MonitoringThreadFactory monitoringFactory = new MonitoringThreadFactory("MonitoredWorker");
        
        MyThreadPool threadPool = new MyThreadPool(
            2, 3, 1, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(2),
            new DiscardPolicyRejectHandle(),
            monitoringFactory
        );
        
        // 提交任务
        for (int i = 0; i < 4; i++) {
            final int taskId = i;
            threadPool.execute(() -> {
                System.out.println("Monitoring factory task " + taskId + 
                    " executed by " + Thread.currentThread().getName());
                try {
                    Thread.sleep(800); // 稍长的执行时间以观察监控效果
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
        }
        
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 打印统计信息
        monitoringFactory.printStatistics();
        threadPool.shutdown();
        System.out.println();
    }
    
    /**
     * 测试自定义线程工厂
     */
    private static void testCustomThreadFactory() {
        System.out.println("--- 测试自定义线程工厂 ---");
        
        // 创建一个自定义线程工厂，设置高优先级和特殊命名
        ThreadFactory customFactory = new ThreadFactory() {
            private int counter = 1;
            
            @Override
            public Thread newThread(Runnable r) {
                Thread thread = new Thread(r, "HighPriority-Worker-" + counter++);
                thread.setPriority(Thread.MAX_PRIORITY); // 设置最高优先级
                thread.setDaemon(false);
                
                // 添加自定义的未捕获异常处理
                thread.setUncaughtExceptionHandler((t, e) -> {
                    System.err.println("High priority thread " + t.getName() + 
                        " encountered error: " + e.getMessage());
                });
                
                System.out.println("Created high priority thread: " + thread.getName() + 
                    " with priority " + thread.getPriority());
                return thread;
            }
        };
        
        MyThreadPool threadPool = new MyThreadPool(
            2, 3, 1, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(2),
            new DiscardPolicyRejectHandle(),
            customFactory
        );
        
        // 提交任务
        for (int i = 0; i < 4; i++) {
            final int taskId = i;
            threadPool.execute(() -> {
                System.out.println("Custom factory task " + taskId + 
                    " executed by " + Thread.currentThread().getName() + 
                    " (priority: " + Thread.currentThread().getPriority() + ")");
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
        }
        
        try {
            Thread.sleep(1500);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        threadPool.shutdown();
    }
}
