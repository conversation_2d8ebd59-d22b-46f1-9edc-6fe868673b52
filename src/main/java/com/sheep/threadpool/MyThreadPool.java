package com.sheep.threadpool;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingDeque;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.TimeUnit;

public class MyThreadPool {

    private final int corePoolSize;

    private final int maxPoolSize;

    private final int keepAliveTime;

    private final TimeUnit timeUnit;

    private final BlockingQueue<Runnable> blockingQueue;

    private final RejectHandle rejectHandle;

    List<Thread> coreList = new ArrayList<>();

    List<Thread> supportList = new ArrayList<>();

    public MyThreadPool(int corePoolSize, int maxPoolSize, int keepAliveTime, TimeUnit timeUnit, BlockingQueue<Runnable> blockingQueue, RejectHandle rejectHandle){

        this.corePoolSize = corePoolSize;
        this.maxPoolSize = maxPoolSize;
        this.keepAliveTime = keepAliveTime;
        this.timeUnit = timeUnit;
        this.blockingQueue = blockingQueue;
        this.rejectHandle = rejectHandle;
    }





    // 判断threadList中有多少个元素，如果没到core pool size 就创建线程
    void execute(Runnable command) {
        if (coreList.size() < corePoolSize) {
            Thread thread = new coreThread();
            coreList.add(thread);
            thread.start();
        }
        if (blockingQueue.offer(command)) {
            return;
        }
        if(coreList.size() + supportList.size() < maxPoolSize) {
            Thread thread = new supportThread();
            supportList.add(thread);
            thread.start();
        }
        if (!blockingQueue.offer(command)) {
            rejectHandle.reject(command, this);
        };
    }

    class coreThread extends Thread {
        @Override
        public void run() {
            while (true) {
                try {
                    Runnable command = blockingQueue.take();
                    command.run();
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }

    class supportThread extends Thread {
        @Override
        public void run() {
            while (true) {
                try {
                    Runnable command = blockingQueue.poll(keepAliveTime, timeUnit);
                    if (command == null) {
                        break;
                    }
                    command.run();
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
            System.out.println(Thread.currentThread().getName() + "is Terminated");
        }
    }
}
