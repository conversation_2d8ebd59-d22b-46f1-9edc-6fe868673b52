package com.sheep.threadpool.util;

import com.sheep.threadpool.core.MyThreadPool;
import com.sheep.threadpool.factory.NamedThreadFactory;
import com.sheep.threadpool.reject.*;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.TimeUnit;

/**
 * 线程池工具类
 * 提供常用的线程池创建方法和工具函数
 */
public class ThreadPoolUtils {
    
    /**
     * 创建固定大小的线程池
     * 核心线程数和最大线程数相等，使用无界队列
     * 
     * @param nThreads 线程数量
     * @param threadNamePrefix 线程名称前缀
     * @return 线程池实例
     */
    public static MyThreadPool newFixedThreadPool(int nThreads, String threadNamePrefix) {
        return new MyThreadPool(
            nThreads, nThreads, 0, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(),
            new CallerRunsPolicyRejectHandle(),
            new NamedThreadFactory(threadNamePrefix)
        );
    }
    
    /**
     * 创建单线程线程池
     * 只有一个线程，任务按顺序执行
     * 
     * @param threadNamePrefix 线程名称前缀
     * @return 线程池实例
     */
    public static MyThreadPool newSingleThreadPool(String threadNamePrefix) {
        return newFixedThreadPool(1, threadNamePrefix);
    }
    
    /**
     * 创建缓存线程池
     * 根据需要创建线程，空闲线程会被回收
     * 
     * @param threadNamePrefix 线程名称前缀
     * @return 线程池实例
     */
    public static MyThreadPool newCachedThreadPool(String threadNamePrefix) {
        return new MyThreadPool(
            0, Integer.MAX_VALUE, 60, TimeUnit.SECONDS,
            new SynchronousQueue<>(),
            new CallerRunsPolicyRejectHandle(),
            new NamedThreadFactory(threadNamePrefix)
        );
    }
    
    /**
     * 创建有界线程池
     * 指定核心线程数、最大线程数和队列容量
     * 
     * @param corePoolSize 核心线程数
     * @param maximumPoolSize 最大线程数
     * @param queueCapacity 队列容量
     * @param threadNamePrefix 线程名称前缀
     * @return 线程池实例
     */
    public static MyThreadPool newBoundedThreadPool(int corePoolSize, int maximumPoolSize, 
                                                   int queueCapacity, String threadNamePrefix) {
        return new MyThreadPool(
            corePoolSize, maximumPoolSize, 60, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(queueCapacity),
            new ThrowRejectHandle(),
            new NamedThreadFactory(threadNamePrefix)
        );
    }
    
    /**
     * 创建IO密集型线程池
     * 适合IO密集型任务，线程数较多
     * 
     * @param threadNamePrefix 线程名称前缀
     * @return 线程池实例
     */
    public static MyThreadPool newIOIntensiveThreadPool(String threadNamePrefix) {
        int processors = Runtime.getRuntime().availableProcessors();
        int corePoolSize = processors * 2;
        int maxPoolSize = processors * 4;
        
        return new MyThreadPool(
            corePoolSize, maxPoolSize, 60, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(1000),
            new CallerRunsPolicyRejectHandle(),
            new NamedThreadFactory(threadNamePrefix)
        );
    }
    
    /**
     * 创建CPU密集型线程池
     * 适合CPU密集型任务，线程数接近CPU核心数
     * 
     * @param threadNamePrefix 线程名称前缀
     * @return 线程池实例
     */
    public static MyThreadPool newCPUIntensiveThreadPool(String threadNamePrefix) {
        int processors = Runtime.getRuntime().availableProcessors();
        
        return new MyThreadPool(
            processors, processors + 1, 60, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(100),
            new CallerRunsPolicyRejectHandle(),
            new NamedThreadFactory(threadNamePrefix)
        );
    }
    
    /**
     * 打印线程池状态信息
     * 
     * @param threadPool 线程池
     * @param poolName 线程池名称
     */
    public static void printThreadPoolStatus(MyThreadPool threadPool, String poolName) {
        System.out.println("=== " + poolName + " Status ===");
        System.out.println("Core Pool Size: " + threadPool.getCorePoolSize());
        System.out.println("Maximum Pool Size: " + threadPool.getMaximumPoolSize());
        System.out.println("Active Count: " + threadPool.getActiveCount());
        System.out.println("Queue Size: " + threadPool.getQueueSize());
        System.out.println("Pool State: " + threadPool.getPoolState());
        System.out.println("Is Shutdown: " + threadPool.isShutdown());
        System.out.println("Is Terminated: " + threadPool.isTerminated());
        System.out.println("========================");
    }
    
    /**
     * 优雅关闭线程池
     * 等待一定时间后强制关闭
     * 
     * @param threadPool 线程池
     * @param timeoutSeconds 等待超时时间（秒）
     */
    public static void gracefulShutdown(MyThreadPool threadPool, int timeoutSeconds) {
        System.out.println("Initiating graceful shutdown...");
        
        // 开始关闭
        threadPool.shutdown();
        
        // 等待指定时间
        try {
            Thread.sleep(timeoutSeconds * 1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 如果还没有终止，强制关闭
        if (!threadPool.isTerminated()) {
            System.out.println("Forcing shutdown...");
            threadPool.shutdownNow();
        }
        
        System.out.println("Shutdown completed.");
    }
    
    /**
     * 获取推荐的线程池配置
     * 根据系统资源和任务类型推荐配置
     * 
     * @param taskType 任务类型：CPU_INTENSIVE, IO_INTENSIVE, MIXED
     * @return 配置建议字符串
     */
    public static String getRecommendedConfig(String taskType) {
        int processors = Runtime.getRuntime().availableProcessors();
        long maxMemory = Runtime.getRuntime().maxMemory() / (1024 * 1024); // MB
        
        StringBuilder config = new StringBuilder();
        config.append("=== Recommended Thread Pool Configuration ===\n");
        config.append("System Info:\n");
        config.append("  CPU Cores: ").append(processors).append("\n");
        config.append("  Max Memory: ").append(maxMemory).append(" MB\n");
        config.append("Task Type: ").append(taskType).append("\n");
        
        switch (taskType.toUpperCase()) {
            case "CPU_INTENSIVE":
                config.append("Recommended Config:\n");
                config.append("  Core Pool Size: ").append(processors).append("\n");
                config.append("  Max Pool Size: ").append(processors + 1).append("\n");
                config.append("  Queue Capacity: 100-500\n");
                config.append("  Keep Alive Time: 60 seconds\n");
                break;
                
            case "IO_INTENSIVE":
                config.append("Recommended Config:\n");
                config.append("  Core Pool Size: ").append(processors * 2).append("\n");
                config.append("  Max Pool Size: ").append(processors * 4).append("\n");
                config.append("  Queue Capacity: 1000-5000\n");
                config.append("  Keep Alive Time: 60 seconds\n");
                break;
                
            case "MIXED":
                config.append("Recommended Config:\n");
                config.append("  Core Pool Size: ").append(processors + 1).append("\n");
                config.append("  Max Pool Size: ").append(processors * 2).append("\n");
                config.append("  Queue Capacity: 500-1000\n");
                config.append("  Keep Alive Time: 60 seconds\n");
                break;
                
            default:
                config.append("Unknown task type. Please specify: CPU_INTENSIVE, IO_INTENSIVE, or MIXED\n");
        }
        
        config.append("============================================");
        return config.toString();
    }
}
