package com.sheep.threadpool.factory;

import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 监控线程工厂
 * 提供线程创建的统计信息和监控功能
 */
public class MonitoringThreadFactory implements ThreadFactory {
    
    /** 线程计数器 */
    private final AtomicInteger threadNumber = new AtomicInteger(1);
    
    /** 创建的线程总数 */
    private final AtomicLong totalCreatedThreads = new AtomicLong(0);
    
    /** 当前活跃线程数 */
    private final AtomicInteger activeThreads = new AtomicInteger(0);
    
    /** 线程名称前缀 */
    private final String namePrefix;
    
    /** 创建时间记录 */
    private final long factoryCreateTime;
    
    /**
     * 构造函数
     * 
     * @param namePrefix 线程名称前缀
     */
    public MonitoringThreadFactory(String namePrefix) {
        this.namePrefix = namePrefix;
        this.factoryCreateTime = System.currentTimeMillis();
    }
    
    @Override
    public Thread newThread(Runnable r) {
        // 包装原始任务，添加监控逻辑
        Runnable wrappedTask = () -> {
            activeThreads.incrementAndGet();
            long startTime = System.currentTimeMillis();
            
            try {
                r.run();
            } finally {
                activeThreads.decrementAndGet();
                long endTime = System.currentTimeMillis();
                System.out.println("Thread " + Thread.currentThread().getName() + 
                    " finished, execution time: " + (endTime - startTime) + "ms");
            }
        };
        
        // 创建线程
        Thread thread = new Thread(wrappedTask, namePrefix + "-" + threadNumber.getAndIncrement());
        
        // 更新统计信息
        totalCreatedThreads.incrementAndGet();
        
        // 设置线程属性
        thread.setDaemon(false);
        thread.setPriority(Thread.NORM_PRIORITY);
        
        // 设置未捕获异常处理器
        thread.setUncaughtExceptionHandler((t, e) -> {
            activeThreads.decrementAndGet();
            System.err.println("Thread " + t.getName() + " terminated due to uncaught exception: " + e.getMessage());
            e.printStackTrace();
        });
        
        System.out.println("Created new thread: " + thread.getName() + 
            " (Total created: " + totalCreatedThreads.get() + ")");
        
        return thread;
    }
    
    /**
     * 获取创建的线程总数
     */
    public long getTotalCreatedThreads() {
        return totalCreatedThreads.get();
    }
    
    /**
     * 获取当前活跃线程数
     */
    public int getActiveThreads() {
        return activeThreads.get();
    }
    
    /**
     * 获取工厂运行时间
     */
    public long getFactoryUptime() {
        return System.currentTimeMillis() - factoryCreateTime;
    }
    
    /**
     * 打印统计信息
     */
    public void printStatistics() {
        System.out.println("=== ThreadFactory Statistics ===");
        System.out.println("Name Prefix: " + namePrefix);
        System.out.println("Total Created Threads: " + totalCreatedThreads.get());
        System.out.println("Current Active Threads: " + activeThreads.get());
        System.out.println("Factory Uptime: " + getFactoryUptime() + "ms");
        System.out.println("================================");
    }
}
