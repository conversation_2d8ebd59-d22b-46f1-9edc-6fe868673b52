package com.sheep.threadpool.factory;

import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 默认线程工厂实现
 * 提供基本的线程创建功能，包括命名和基本属性设置
 */
public class DefaultThreadFactory implements ThreadFactory {
    
    /** 线程计数器，确保每个线程有唯一的编号 */
    private final AtomicInteger threadNumber = new AtomicInteger(1);
    
    /** 线程名称前缀 */
    private final String namePrefix;

    /**
     * 默认构造函数
     */
    public DefaultThreadFactory() {
        this.namePrefix = "MyThreadPool-Thread-";
    }

    /**
     * 构造函数，可指定线程名称前缀
     * 
     * @param namePrefix 线程名称前缀
     */
    public DefaultThreadFactory(String namePrefix) {
        this.namePrefix = namePrefix;
    }

    @Override
    public Thread newThread(Runnable r) {
        Thread thread = new Thread(r, namePrefix + threadNumber.getAndIncrement());
        
        // 设置为非守护线程
        if (thread.isDaemon()) {
            thread.setDaemon(false);
        }
        
        // 设置正常优先级
        if (thread.getPriority() != Thread.NORM_PRIORITY) {
            thread.setPriority(Thread.NORM_PRIORITY);
        }
        
        return thread;
    }
}
