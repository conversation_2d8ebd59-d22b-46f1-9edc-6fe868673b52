package com.sheep.threadpool.core;

import com.sheep.threadpool.factory.DefaultThreadFactory;
import com.sheep.threadpool.reject.RejectHandle;
import com.sheep.threadpool.worker.CoreWorker;
import com.sheep.threadpool.worker.SupportWorker;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 自定义线程池实现
 * 模仿 Java ThreadPoolExecutor 的核心功能
 * 
 * 线程池执行策略：
 * 1. 如果当前运行的线程数少于核心线程数，创建新的核心线程执行任务
 * 2. 如果核心线程都在忙碌，尝试将任务加入工作队列
 * 3. 如果队列已满且当前线程数少于最大线程数，创建新的支持线程
 * 4. 如果队列已满且已达到最大线程数，执行拒绝策略
 */
public class MyThreadPool {

    // ==================== 线程池配置参数 ====================
    
    /** 核心线程数：线程池中始终保持的线程数量 */
    private final int corePoolSize;

    /** 最大线程数：线程池中允许的最大线程数量 */
    private final int maxPoolSize;

    /** 非核心线程的空闲存活时间 */
    private final int keepAliveTime;

    /** 存活时间的时间单位 */
    private final TimeUnit timeUnit;

    /** 任务队列：用于存储等待执行的任务 */
    private final BlockingQueue<Runnable> blockingQueue;

    /** 拒绝策略：当线程池无法接受新任务时的处理策略 */
    private final RejectHandle rejectHandle;

    /** 线程工厂：用于创建新线程，可以自定义线程属性 */
    private final ThreadFactory threadFactory;

    // ==================== 线程池状态管理 ====================
    
    /** 当前线程池状态 */
    private volatile PoolState poolState = PoolState.RUNNING;

    /** 当前活跃的核心线程数量 */
    private final AtomicInteger activeCoreThreads = new AtomicInteger(0);

    /** 当前活跃的支持线程数量 */
    private final AtomicInteger activeSupportThreads = new AtomicInteger(0);

    /** 线程安全的核心线程集合 */
    private final ConcurrentLinkedQueue<Thread> coreThreads = new ConcurrentLinkedQueue<>();

    /** 线程安全的支持线程集合 */
    private final ConcurrentLinkedQueue<Thread> supportThreads = new ConcurrentLinkedQueue<>();

    /** 用于同步execute方法的锁，确保线程创建的原子性 */
    private final ReentrantLock executeLock = new ReentrantLock();

    /**
     * 构造函数：初始化线程池（使用默认线程工厂）
     * 
     * @param corePoolSize 核心线程数
     * @param maxPoolSize 最大线程数
     * @param keepAliveTime 非核心线程空闲存活时间
     * @param timeUnit 时间单位
     * @param blockingQueue 任务队列
     * @param rejectHandle 拒绝策略
     */
    public MyThreadPool(int corePoolSize, int maxPoolSize, int keepAliveTime, 
                       TimeUnit timeUnit, BlockingQueue<Runnable> blockingQueue, 
                       RejectHandle rejectHandle) {
        this(corePoolSize, maxPoolSize, keepAliveTime, timeUnit, blockingQueue, 
             rejectHandle, new DefaultThreadFactory());
    }

    /**
     * 构造函数：初始化线程池（完整版本）
     * 
     * @param corePoolSize 核心线程数
     * @param maxPoolSize 最大线程数
     * @param keepAliveTime 非核心线程空闲存活时间
     * @param timeUnit 时间单位
     * @param blockingQueue 任务队列
     * @param rejectHandle 拒绝策略
     * @param threadFactory 线程工厂
     */
    public MyThreadPool(int corePoolSize, int maxPoolSize, int keepAliveTime, 
                       TimeUnit timeUnit, BlockingQueue<Runnable> blockingQueue, 
                       RejectHandle rejectHandle, ThreadFactory threadFactory) {
        
        // 参数校验
        if (corePoolSize < 0 || maxPoolSize <= 0 || maxPoolSize < corePoolSize || keepAliveTime < 0) {
            throw new IllegalArgumentException("Invalid thread pool parameters");
        }
        if (blockingQueue == null || rejectHandle == null || threadFactory == null) {
            throw new NullPointerException("BlockingQueue, RejectHandle and ThreadFactory cannot be null");
        }

        this.corePoolSize = corePoolSize;
        this.maxPoolSize = maxPoolSize;
        this.keepAliveTime = keepAliveTime;
        this.timeUnit = timeUnit;
        this.blockingQueue = blockingQueue;
        this.rejectHandle = rejectHandle;
        this.threadFactory = threadFactory;
    }

    // ==================== 核心方法：任务执行 ====================

    /**
     * 执行任务的核心方法
     * 按照标准线程池策略执行：核心线程 -> 队列 -> 支持线程 -> 拒绝策略
     * 
     * @param command 要执行的任务
     * @throws IllegalStateException 如果线程池已关闭
     */
    public void execute(Runnable command) {
        // 1. 参数校验
        if (command == null) {
            throw new NullPointerException("Task cannot be null");
        }

        // 2. 检查线程池状态
        if (poolState != PoolState.RUNNING) {
            throw new IllegalStateException("ThreadPool is not running");
        }

        // 3. 清理已终止的线程（维护线程池健康状态）
        cleanupTerminatedThreads();

        // 4. 使用锁确保线程创建的原子性，避免并发问题
        executeLock.lock();
        try {
            // 步骤1：如果核心线程数未达到上限，优先创建核心线程
            if (activeCoreThreads.get() < corePoolSize) {
                if (createCoreThread()) {
                    // 核心线程创建成功，将任务加入队列，由新创建的线程处理
                    blockingQueue.offer(command);
                    return;
                }
            }

            // 步骤2：尝试将任务加入队列
            if (blockingQueue.offer(command)) {
                // 任务成功加入队列，由现有线程处理
                return;
            }

            // 步骤3：队列已满，尝试创建支持线程
            if (getTotalActiveThreads() < maxPoolSize) {
                if (createSupportThread()) {
                    // 支持线程创建成功，将任务加入队列
                    blockingQueue.offer(command);
                    return;
                }
            }

            // 步骤4：无法处理任务，执行拒绝策略
            rejectHandle.reject(command, this);

        } finally {
            executeLock.unlock();
        }
    }

    // ==================== 线程创建和管理方法 ====================

    /**
     * 创建核心线程
     *
     * @return true 如果成功创建，false 如果创建失败
     */
    private boolean createCoreThread() {
        Thread thread = null;
        boolean counterIncremented = false;

        try {
            // 使用线程工厂创建核心线程
            thread = threadFactory.newThread(new CoreWorker(this, keepAliveTime, timeUnit));
            if (thread == null) {
                return false;
            }

            // 先增加计数器，确保状态一致性
            activeCoreThreads.incrementAndGet();
            counterIncremented = true;

            // 添加到线程集合
            coreThreads.offer(thread);

            // 启动线程
            thread.start();
            return true;

        } catch (Exception e) {
            // 只有在计数器已经增加的情况下才回滚
            if (counterIncremented) {
                activeCoreThreads.decrementAndGet();
            }

            // 如果线程已经启动但出现异常，需要中断线程
            if (thread != null && thread.isAlive()) {
                thread.interrupt();
            }

            System.err.println("Failed to create core thread: " + e.getMessage());
            return false;
        }
    }

    /**
     * 创建支持线程（临时线程）
     * 
     * @return true 如果成功创建，false 如果创建失败
     */
    private boolean createSupportThread() {
        try {
            // 使用线程工厂创建支持线程
            Thread thread = threadFactory.newThread(new SupportWorker(this, keepAliveTime, timeUnit));
            if (thread == null) {
                return false;
            }
            
            supportThreads.offer(thread);
            activeSupportThreads.incrementAndGet();
            thread.start();
            return true;
        } catch (Exception e) {
            // 创建失败，回滚计数器
            activeSupportThreads.decrementAndGet();
            return false;
        }
    }

    /**
     * 获取当前活跃线程总数
     * 
     * @return 活跃线程总数
     */
    private int getTotalActiveThreads() {
        return activeCoreThreads.get() + activeSupportThreads.get();
    }

    /**
     * 清理已终止的线程
     * 定期清理死亡线程，避免内存泄漏
     */
    private void cleanupTerminatedThreads() {
        // 清理核心线程
        coreThreads.removeIf(thread -> {
            if (!thread.isAlive()) {
                activeCoreThreads.decrementAndGet();
                return true;
            }
            return false;
        });

        // 清理支持线程
        supportThreads.removeIf(thread -> {
            if (!thread.isAlive()) {
                activeSupportThreads.decrementAndGet();
                return true;
            }
            return false;
        });
    }

    // ==================== 线程池状态查询方法 ====================

    /**
     * 获取核心线程数
     */
    public int getCorePoolSize() {
        return corePoolSize;
    }

    /**
     * 获取最大线程数
     */
    public int getMaximumPoolSize() {
        return maxPoolSize;
    }

    /**
     * 获取当前活跃线程数
     */
    public int getActiveCount() {
        cleanupTerminatedThreads();
        return getTotalActiveThreads();
    }

    /**
     * 获取队列中等待的任务数
     */
    public int getQueueSize() {
        return blockingQueue.size();
    }

    /**
     * 检查线程池是否已关闭
     */
    public boolean isShutdown() {
        return poolState != PoolState.RUNNING;
    }

    /**
     * 检查线程池是否已终止
     */
    public boolean isTerminated() {
        return poolState == PoolState.TERMINATED;
    }

    /**
     * 获取当前线程池状态
     */
    public PoolState getPoolState() {
        return poolState;
    }

    // ==================== 线程池关闭方法 ====================

    /**
     * 优雅关闭线程池
     * 不再接受新任务，但会等待已提交的任务执行完成
     */
    public void shutdown() {
        executeLock.lock();
        try {
            if (poolState == PoolState.RUNNING) {
                poolState = PoolState.SHUTDOWN;
                
                // 中断所有线程，让它们有机会优雅退出
                interruptAllThreads();
            }
        } finally {
            executeLock.unlock();
        }
    }

    /**
     * 立即关闭线程池
     * 不再接受新任务，尝试停止所有正在执行的任务
     */
    public void shutdownNow() {
        executeLock.lock();
        try {
            poolState = PoolState.TERMINATED;
            
            // 清空队列
            blockingQueue.clear();
            
            // 强制中断所有线程
            interruptAllThreads();
        } finally {
            executeLock.unlock();
        }
    }

    /**
     * 中断所有线程
     */
    private void interruptAllThreads() {
        // 中断核心线程
        for (Thread thread : coreThreads) {
            if (thread.isAlive()) {
                thread.interrupt();
            }
        }
        
        // 中断支持线程
        for (Thread thread : supportThreads) {
            if (thread.isAlive()) {
                thread.interrupt();
            }
        }
    }

    // ==================== 内部方法 ====================

    /**
     * 安全执行任务的方法
     * 包装任务执行，处理可能的异常
     * 
     * @param command 要执行的任务
     */
    public void executeTask(Runnable command) {
        try {
            // 执行用户提交的任务
            command.run();
        } catch (Exception e) {
            // 捕获任务执行中的异常，避免线程因异常而终止
            System.err.println("Task execution failed in thread " + 
                             Thread.currentThread().getName() + ": " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 减少核心线程计数
     */
    public void decrementCoreThreadCount() {
        activeCoreThreads.decrementAndGet();
    }

    /**
     * 减少支持线程计数
     */
    public void decrementSupportThreadCount() {
        activeSupportThreads.decrementAndGet();
    }

    // ==================== 包内访问方法 ====================

    /**
     * 获取任务队列（包内访问）
     *
     * @return 任务队列
     */
    public BlockingQueue<Runnable> getBlockingQueue() {
        return blockingQueue;
    }
}
