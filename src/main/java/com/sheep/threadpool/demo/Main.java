package com.sheep.threadpool.demo;

import com.sheep.threadpool.core.MyThreadPool;
import com.sheep.threadpool.factory.NamedThreadFactory;
import com.sheep.threadpool.reject.*;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * 线程池测试类
 * 演示自定义线程池的各种功能和拒绝策略
 */
public class Main {
    public static void main(String[] args) throws InterruptedException {
        System.out.println("=== 自定义线程池测试开始 ===");
        
        // 测试不同的拒绝策略
        testDiscardOldestPolicy();
        Thread.sleep(2000);
        
        testCallerRunsPolicy();
        Thread.sleep(2000);
        
        testThrowPolicy();
        Thread.sleep(2000);
        
        testShutdown();
        
        System.out.println("=== 所有测试完成 ===");
    }
    
    /**
     * 测试丢弃最旧任务的拒绝策略
     */
    private static void testDiscardOldestPolicy() {
        System.out.println("\n--- 测试丢弃最旧任务策略 ---");
        
        // 创建线程池：2个核心线程，4个最大线程，队列容量2，支持线程存活1秒
        MyThreadPool threadPool = new MyThreadPool(
            2, 4, 1, TimeUnit.SECONDS, 
            new ArrayBlockingQueue<>(2), 
            new DiscardRejectHandle(),
            new NamedThreadFactory("DiscardTest")
        );
        
        // 提交8个任务，超过线程池容量
        for (int i = 0; i < 8; i++) {
            final int taskId = i;
            threadPool.execute(() -> {
                try {
                    System.out.println("Task " + taskId + " started by " + Thread.currentThread().getName());
                    Thread.sleep(1500); // 模拟任务执行时间
                    System.out.println("Task " + taskId + " completed by " + Thread.currentThread().getName());
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    System.out.println("Task " + taskId + " interrupted");
                }
            });
            
            // 打印当前线程池状态
            System.out.println("After submitting task " + taskId + 
                " - Active threads: " + threadPool.getActiveCount() + 
                ", Queue size: " + threadPool.getQueueSize());
        }
        
        // 等待任务完成
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        threadPool.shutdown();
    }
    
    /**
     * 测试调用者运行策略
     */
    private static void testCallerRunsPolicy() {
        System.out.println("\n--- 测试调用者运行策略 ---");
        
        MyThreadPool threadPool = new MyThreadPool(
            1, 2, 1, TimeUnit.SECONDS, 
            new ArrayBlockingQueue<>(1), 
            new CallerRunsPolicyRejectHandle(),
            new NamedThreadFactory("CallerRunsTest")
        );
        
        // 提交5个任务
        for (int i = 0; i < 5; i++) {
            final int taskId = i;
            threadPool.execute(() -> {
                System.out.println("Task " + taskId + " executed by " + Thread.currentThread().getName());
                try {
                    Thread.sleep(800);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
        }
        
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        threadPool.shutdown();
    }
    
    /**
     * 测试抛出异常策略
     */
    private static void testThrowPolicy() {
        System.out.println("\n--- 测试抛出异常策略 ---");
        
        MyThreadPool threadPool = new MyThreadPool(
            1, 1, 1, TimeUnit.SECONDS, 
            new ArrayBlockingQueue<>(1), 
            new ThrowRejectHandle(),
            new NamedThreadFactory("ThrowTest")
        );
        
        // 提交任务直到触发拒绝策略
        for (int i = 0; i < 4; i++) {
            final int taskId = i;
            try {
                threadPool.execute(() -> {
                    System.out.println("Task " + taskId + " executed by " + Thread.currentThread().getName());
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                });
                System.out.println("Task " + taskId + " submitted successfully");
            } catch (RuntimeException e) {
                System.out.println("Task " + taskId + " rejected: " + e.getMessage());
            }
        }
        
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        threadPool.shutdown();
    }
    
    /**
     * 测试线程池关闭功能
     */
    private static void testShutdown() {
        System.out.println("\n--- 测试线程池关闭功能 ---");
        
        MyThreadPool threadPool = new MyThreadPool(
            2, 2, 1, TimeUnit.SECONDS, 
            new ArrayBlockingQueue<>(3), 
            new DiscardPolicyRejectHandle(),
            new NamedThreadFactory("ShutdownTest")
        );
        
        // 提交一些任务
        for (int i = 0; i < 3; i++) {
            final int taskId = i;
            threadPool.execute(() -> {
                System.out.println("Shutdown test task " + taskId + " started");
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    System.out.println("Shutdown test task " + taskId + " interrupted");
                    return;
                }
                System.out.println("Shutdown test task " + taskId + " completed");
            });
        }
        
        // 等待一段时间后关闭线程池
        try {
            Thread.sleep(500);
            System.out.println("Shutting down thread pool...");
            threadPool.shutdown();
            
            // 尝试提交新任务（应该失败）
            try {
                threadPool.execute(() -> System.out.println("This should not execute"));
            } catch (IllegalStateException e) {
                System.out.println("Expected: " + e.getMessage());
            }
            
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}
