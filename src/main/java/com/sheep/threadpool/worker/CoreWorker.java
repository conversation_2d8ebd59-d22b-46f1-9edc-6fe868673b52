package com.sheep.threadpool.worker;

import com.sheep.threadpool.core.MyThreadPool;
import com.sheep.threadpool.core.PoolState;

import java.util.concurrent.TimeUnit;

/**
 * 核心线程工作器
 * 核心线程会持续运行，直到线程池关闭
 */
public class CoreWorker implements Runnable {
    
    /** 所属的线程池 */
    private final MyThreadPool threadPool;
    
    /** 保活时间（虽然核心线程不使用，但保持接口一致性） */
    private final int keepAliveTime;
    
    /** 时间单位 */
    private final TimeUnit timeUnit;
    
    /**
     * 构造函数
     * 
     * @param threadPool 所属的线程池
     * @param keepAliveTime 保活时间
     * @param timeUnit 时间单位
     */
    public CoreWorker(MyThreadPool threadPool, int keepAliveTime, TimeUnit timeUnit) {
        this.threadPool = threadPool;
        this.keepAliveTime = keepAliveTime;
        this.timeUnit = timeUnit;
    }

    @Override
    public void run() {
        try {
            // 核心线程持续运行，直到被中断或线程池关闭
            while (!Thread.currentThread().isInterrupted() && 
                   threadPool.getPoolState() == PoolState.RUNNING) {
                try {
                    // 阻塞等待任务，take()会一直等待直到有任务或被中断
                    Runnable command = threadPool.getBlockingQueue().take();

                    // 执行任务前再次检查线程池状态
                    if (threadPool.getPoolState() == PoolState.RUNNING) {
                        threadPool.executeTask(command);
                    } else {
                        // 线程池已关闭，将任务放回队列
                        threadPool.getBlockingQueue().offer(command);
                        break;
                    }
                    
                } catch (InterruptedException e) {
                    // 收到中断信号，准备退出
                    Thread.currentThread().interrupt(); // 恢复中断状态
                    break;
                }
            }
        } finally {
            // 线程退出时减少计数器
            threadPool.decrementCoreThreadCount();
            System.out.println(Thread.currentThread().getName() + " (Core) terminated");
        }
    }
}
