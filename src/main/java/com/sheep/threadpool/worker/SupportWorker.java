package com.sheep.threadpool.worker;

import com.sheep.threadpool.core.MyThreadPool;
import com.sheep.threadpool.core.PoolState;

import java.util.concurrent.TimeUnit;

/**
 * 支持线程工作器（临时线程）
 * 支持线程有超时机制，空闲一定时间后会自动退出
 */
public class SupportWorker implements Runnable {
    
    /** 所属的线程池 */
    private final MyThreadPool threadPool;
    
    /** 保活时间 */
    private final int keepAliveTime;
    
    /** 时间单位 */
    private final TimeUnit timeUnit;
    
    /**
     * 构造函数
     * 
     * @param threadPool 所属的线程池
     * @param keepAliveTime 保活时间
     * @param timeUnit 时间单位
     */
    public SupportWorker(MyThreadPool threadPool, int keepAliveTime, TimeUnit timeUnit) {
        this.threadPool = threadPool;
        this.keepAliveTime = keepAliveTime;
        this.timeUnit = timeUnit;
    }

    @Override
    public void run() {
        try {
            // 支持线程运行直到超时、被中断或线程池关闭
            while (!Thread.currentThread().isInterrupted() && 
                   threadPool.getPoolState() == PoolState.RUNNING) {
                try {
                    // 带超时的等待任务，如果超时则退出
                    Runnable command = threadPool.getBlockingQueue().poll(keepAliveTime, timeUnit);

                    if (command == null) {
                        // 超时未获取到任务，支持线程退出
                        break;
                    }

                    // 执行任务前再次检查线程池状态
                    if (threadPool.getPoolState() == PoolState.RUNNING) {
                        threadPool.executeTask(command);
                    } else {
                        // 线程池已关闭，将任务放回队列
                        threadPool.getBlockingQueue().offer(command);
                        break;
                    }
                    
                } catch (InterruptedException e) {
                    // 收到中断信号，准备退出
                    Thread.currentThread().interrupt(); // 恢复中断状态
                    break;
                }
            }
        } finally {
            // 线程退出时减少计数器
            threadPool.decrementSupportThreadCount();
            System.out.println(Thread.currentThread().getName() + " (Support) terminated");
        }
    }
}
