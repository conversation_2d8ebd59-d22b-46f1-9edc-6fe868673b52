package com.sheep.threadpool.reject;

import com.sheep.threadpool.core.MyThreadPool;

/**
 * 抛出异常的拒绝策略
 * 当线程池无法接受新任务时，直接抛出异常
 */
public class ThrowRejectHandle implements RejectHandle {
    
    @Override
    public void reject(Runnable rejectCommand, MyThreadPool threadPool) {
        // 抛出运行时异常，通知调用者任务被拒绝
        throw new RuntimeException("Task rejected: ThreadPool is full. " +
                "Active threads: " + threadPool.getActiveCount() + 
                ", Queue size: " + threadPool.getQueueSize() + 
                ", Max pool size: " + threadPool.getMaximumPoolSize());
    }
}
