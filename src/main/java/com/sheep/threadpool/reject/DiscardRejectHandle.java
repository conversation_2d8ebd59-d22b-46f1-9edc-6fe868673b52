package com.sheep.threadpool.reject;

import com.sheep.threadpool.core.MyThreadPool;

/**
 * 丢弃最旧任务的拒绝策略
 * 当线程池无法接受新任务时，丢弃队列中最旧的任务，然后尝试重新提交当前任务
 */
public class DiscardRejectHandle implements RejectHandle {
    
    @Override
    public void reject(Runnable rejectCommand, MyThreadPool threadPool) {
        // 从队列头部移除最旧的任务（如果队列不为空）
        Runnable discardedTask = threadPool.getBlockingQueue().poll();

        if (discardedTask != null) {
            System.out.println("Discarded oldest task to make room for new task");

            // 尝试将新任务加入队列
            if (!threadPool.getBlockingQueue().offer(rejectCommand)) {
                // 如果仍然无法加入队列，直接丢弃新任务
                System.out.println("Failed to add new task even after discarding oldest task");
            }
        } else {
            // 队列为空，直接丢弃新任务
            System.out.println("Queue is empty, discarding new task");
        }
    }
}
