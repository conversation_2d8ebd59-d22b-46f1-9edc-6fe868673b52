package com.sheep.threadpool.reject;

import com.sheep.threadpool.core.MyThreadPool;

/**
 * 简单丢弃策略
 * 当线程池无法接受新任务时，直接丢弃任务，不做任何处理
 */
public class DiscardPolicyRejectHandle implements RejectHandle {
    
    @Override
    public void reject(Runnable rejectCommand, MyThreadPool threadPool) {
        // 静默丢弃任务，不做任何处理
        System.out.println("Task discarded silently due to thread pool capacity limit");
    }
}
