# 自定义线程池实现 - 改进版

## 📋 项目概述

这是一个自定义的Java线程池实现，模仿了`ThreadPoolExecutor`的核心功能。通过详细的注释和完善的功能，展示了线程池的工作原理和最佳实践。

## 🎯 设计目标

- **教育性**：通过详细注释帮助理解线程池原理
- **功能完整**：实现核心线程、支持线程、任务队列、拒绝策略等功能
- **线程安全**：使用适当的并发工具确保线程安全
- **可扩展性**：支持多种拒绝策略，易于扩展

## 🏗️ 架构设计

### 核心组件

1. **MyThreadPool** - 主线程池类
2. **CoreThread** - 核心线程（永久存在）
3. **SupportThread** - 支持线程（有超时机制）
4. **RejectHandle** - 拒绝策略接口
5. **多种拒绝策略实现**

### 执行策略

线程池按照以下顺序处理任务：

1. **核心线程检查**：如果核心线程数 < corePoolSize，创建新的核心线程
2. **队列入队**：尝试将任务加入工作队列
3. **支持线程创建**：如果队列满了且总线程数 < maxPoolSize，创建支持线程
4. **拒绝策略**：如果无法处理任务，执行拒绝策略

## 🔧 主要改进

### 原始代码问题
- ❌ 线程安全问题（使用ArrayList）
- ❌ execute方法逻辑错误
- ❌ 重复的队列操作
- ❌ 拒绝策略可能导致无限递归
- ❌ 缺少线程池状态管理
- ❌ 异常处理不当

### 改进后的特性
- ✅ **线程安全**：使用ConcurrentLinkedQueue和AtomicInteger
- ✅ **正确的执行逻辑**：按照标准线程池策略执行
- ✅ **状态管理**：支持RUNNING、SHUTDOWN、TERMINATED状态
- ✅ **优雅关闭**：支持shutdown()和shutdownNow()
- ✅ **异常处理**：正确处理中断和任务异常
- ✅ **线程清理**：自动清理已终止的线程
- ✅ **详细注释**：每个步骤都有清晰的注释说明

## 📦 类结构

### MyThreadPool
```java
// 核心配置
private final int corePoolSize;        // 核心线程数
private final int maxPoolSize;         // 最大线程数
private final int keepAliveTime;       // 支持线程存活时间
private final TimeUnit timeUnit;       // 时间单位
private final BlockingQueue<Runnable> blockingQueue;  // 任务队列
private final RejectHandle rejectHandle;              // 拒绝策略

// 状态管理
private volatile PoolState poolState;  // 线程池状态
private final AtomicInteger activeCoreThreads;     // 活跃核心线程数
private final AtomicInteger activeSupportThreads;  // 活跃支持线程数
```

### 拒绝策略

1. **DiscardRejectHandle** - 丢弃最旧任务策略
2. **ThrowRejectHandle** - 抛出异常策略
3. **DiscardPolicyRejectHandle** - 静默丢弃策略
4. **CallerRunsPolicyRejectHandle** - 调用者运行策略

## 🚀 使用示例

### 基本使用
```java
// 创建线程池：2个核心线程，4个最大线程，队列容量2
MyThreadPool threadPool = new MyThreadPool(
    2, 4, 1, TimeUnit.SECONDS,
    new ArrayBlockingQueue<>(2),
    new DiscardRejectHandle()
);

// 提交任务
threadPool.execute(() -> {
    System.out.println("Task executed by " + Thread.currentThread().getName());
});

// 关闭线程池
threadPool.shutdown();
```

### 不同拒绝策略
```java
// 抛出异常策略
new MyThreadPool(2, 4, 1, TimeUnit.SECONDS, 
    new ArrayBlockingQueue<>(2), new ThrowRejectHandle());

// 调用者运行策略
new MyThreadPool(2, 4, 1, TimeUnit.SECONDS, 
    new ArrayBlockingQueue<>(2), new CallerRunsPolicyRejectHandle());
```

## 🧪 测试验证

项目包含完整的单元测试，验证以下功能：

- ✅ 基本任务执行
- ✅ 核心线程创建
- ✅ 支持线程创建
- ✅ 队列容量管理
- ✅ 拒绝策略触发
- ✅ 线程池关闭

运行测试：
```bash
# 编译
mvn compile

# 运行主测试
java -cp target/classes com.sheep.threadpool.Main

# 运行单元测试
javac -cp target/classes -d target/test-classes src/test/java/com/sheep/threadpool/MyThreadPoolTest.java
java -cp "target/classes;target/test-classes" com.sheep.threadpool.MyThreadPoolTest
```

## 📚 学习要点

### 线程池核心概念
1. **核心线程 vs 支持线程**：核心线程永久存在，支持线程有超时机制
2. **任务队列**：缓存等待执行的任务
3. **拒绝策略**：处理无法接受的任务
4. **线程池状态**：RUNNING、SHUTDOWN、TERMINATED

### 并发编程最佳实践
1. **使用线程安全的集合**：ConcurrentLinkedQueue、AtomicInteger
2. **正确处理中断**：恢复中断状态，优雅退出
3. **异常隔离**：任务异常不应影响线程池运行
4. **资源清理**：及时清理已终止的线程

### 设计模式应用
1. **策略模式**：拒绝策略的实现
2. **模板方法**：线程执行流程的定义
3. **状态模式**：线程池状态管理

## 🔍 性能考虑

- **锁的使用**：仅在必要时使用锁，避免过度同步
- **内存管理**：及时清理死亡线程，避免内存泄漏
- **CPU利用率**：合理设置核心线程数和最大线程数
- **队列选择**：根据场景选择合适的阻塞队列

## 🎓 总结

这个改进版的线程池实现展示了：
- 如何正确实现线程池的核心逻辑
- 并发编程中的线程安全考虑
- 异常处理和资源管理的最佳实践
- 详细的代码注释和文档的重要性

通过学习这个实现，可以更好地理解Java并发编程和线程池的工作原理。
